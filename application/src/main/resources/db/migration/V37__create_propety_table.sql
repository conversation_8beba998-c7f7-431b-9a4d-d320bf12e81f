CREATE TABLE IF NOT EXISTS properties
(
    id                      VARCHAR                     NOT NULL,
    name                    VA<PERSON>HAR                     NOT NULL,
    address                 VARCHAR                     NOT NULL,
    city                    VARCHAR                     NOT NULL,
    state                   VARCHAR                     NOT NULL,
    zip_code                VARCHAR                     NOT NULL,
    latitude                NUMERIC                     NOT NULL,
    longitude               NUMERIC                     NOT NULL,
    geolocation             GEOGRAPHY                   NOT NULL,
    property_amenities      VARCHAR[],
    units_amenities         VARCHAR[],
    housing_segments        VARCHAR[],
    property_style          VARCHAR,
    units_quantity          INT                         NOT NULL,
    construction_year       INT,
    stage                   VARCHAR,
    organization_id         VARCHAR                     NOT NULL,
    created_by              <PERSON><PERSON><PERSON><PERSON>,
    created_at              TIMESTAMP(3) WITH TIME ZONE NOT NULL,
    updated_at              TIMESTAMP(3) WITH TIME ZONE NOT NULL,
    constraint properties_pk_id primary key (id)
);