package com.keyway.application.router.property

import com.keyway.adapters.dtos.property.CreatePropertyInput
import com.keyway.adapters.dtos.property.MultifamilyProperty
import com.keyway.adapters.dtos.property.UpdatePropertyInput
import com.keyway.adapters.exceptions.BadRequestException
import com.keyway.adapters.handlers.property.CreatePropertyHandler
import com.keyway.adapters.handlers.property.GetPropertyByIdHandler
import com.keyway.application.ktor.PrincipalUserToken
import com.keyway.application.router.Router
import com.keyway.application.utils.ktor.AuthProvider
import com.keyway.application.utils.ktor.getPropertyId
import com.keyway.application.utils.ktor.readBody
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.patch
import io.github.smiley4.ktoropenapi.post
import io.ktor.http.HttpStatusCode
import io.ktor.server.auth.authenticate
import io.ktor.server.auth.principal
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing

class PropertyRouter(
    private val createPropertyHandler: CreatePropertyHandler,
    private val getPropertyByIdHandler: GetPropertyByIdHandler,
) : Router {
    private val tag = "properties"
    private val baseUrl = "/properties"

    override fun setUpRoutes(routing: Routing) {
        routing.authenticate(AuthProvider.AUTHENTICATED_ENDPOINT) {
            get(
                "$baseUrl/{propertyId}",
                {
                    tags = listOf(tag)
                    summary = "Get Property Details"
                    description = """Retrieves detailed information about a specific property by its ID. This includes property attributes, amenities, and other relevant data."""
                    response {
                        HttpStatusCode.OK to { body<MultifamilyProperty>() }
                    }
                    request {
                        pathParameter<String>("propertyId")
                    }
                },
            ) {
                call.principal<PrincipalUserToken>()?.let { user ->
                    val result =
                        getPropertyByIdHandler(
                            propertyId = call.getPropertyId("propertyId"),
                            organizationId = user.claims.getOrganizationIdOrFail(),
                        )
                    call.respond(
                        status = HttpStatusCode.Created,
                        message = result,
                    )
                }
            }

            patch(
                "$baseUrl/{propertyId}",
                {
                    tags = listOf(tag)
                    summary = "Update Property Information"
                    description = """Updates the information for a specific property. This allows modifying property attributes, amenities, and other relevant data."""
                    response {
                        HttpStatusCode.OK to { body<MultifamilyProperty>() }
                    }
                    request {
                        pathParameter<String>("propertyId")
                        body<UpdatePropertyInput>()
                    }
                },
            ) {
                call.respond("Property update endpoint - not implemented yet")
            }

            get(
                baseUrl,
                {
                    tags = listOf(tag)
                    summary = "Get All Organization Properties"
                    description = """Retrieves all properties associated with the authenticated user's organization. This provides a comprehensive list of properties available for analysis."""
                    response {
                        HttpStatusCode.OK to { body<List<MultifamilyProperty>>() }
                    }
                },
            ) {
                call.respond("Get organization properties endpoint - not implemented yet")
            }

            post(
                baseUrl,
                {
                    tags = listOf(tag)
                    summary = "Create a New Property"
                    description = """Creates a new property in the system for the authenticated user's organization. This allows adding new properties for comparative analysis."""
                    response {
                        HttpStatusCode.Created to { body<MultifamilyProperty>() }
                    }
                    request {
                        body<CreatePropertyInput>()
                    }
                },
            ) {
                call.principal<PrincipalUserToken>()?.let { user ->
                    val result =
                        createPropertyHandler(
                            input =
                                runCatching { call.readBody(CreatePropertyInput::class.java) }
                                    .getOrElse { throw BadRequestException("Invalid request body") },
                            organizationId = user.claims.getOrganizationIdOrFail(),
                            token = user.token,
                        )
                    call.respond(
                        status = HttpStatusCode.Created,
                        message = result,
                    )
                }
            }
        }
    }
}
