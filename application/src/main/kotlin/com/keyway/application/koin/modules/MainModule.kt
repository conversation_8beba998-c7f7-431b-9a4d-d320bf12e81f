package com.keyway.application.koin.modules

import com.auth0.jwk.UrlJwkProvider
import com.keyway.adapters.handlers.HealthCheckHandler
import com.keyway.adapters.handlers.comparable.CompareCompSetPropertiesHandler
import com.keyway.adapters.handlers.comparable.CompareTwoPropertiesHandler
import com.keyway.adapters.handlers.comparable.GetPropertyComparableHandler
import com.keyway.adapters.handlers.compset.CreateCompSetHandler
import com.keyway.adapters.handlers.compset.DeleteCompSetHandler
import com.keyway.adapters.handlers.compset.GetAllCompSetsHandler
import com.keyway.adapters.handlers.compset.GetCompSetByIdHandler
import com.keyway.adapters.handlers.compset.UpdateCompSetHandler
import com.keyway.adapters.handlers.compset.access.policy.CreateCompSetAccessPolicyHandler
import com.keyway.adapters.handlers.compset.access.policy.DeleteCompSetAccessPolicyHandler
import com.keyway.adapters.handlers.compset.access.policy.GetComSetPermissionGroupHandler
import com.keyway.adapters.handlers.compset.access.policy.GetCompSetByIdAccessPolicyHandler
import com.keyway.adapters.handlers.property.CreatePropertyHandler
import com.keyway.adapters.handlers.property.GetPropertyByIdHandler
import com.keyway.application.configuration.model.Auth0TokenVerificationConfig
import com.keyway.application.configuration.model.Configuration
import com.keyway.application.configuration.parser.ConfigParser
import com.keyway.application.koin.ModuleConstants
import com.keyway.application.koin.ModuleConstants.ROUTES
import com.keyway.application.ktor.KtorApp
import com.keyway.application.mapper.AppMapperConfigs.lowerCamelCaseObjectMapper
import com.keyway.application.router.comparables.ComparableRouter
import com.keyway.application.router.compset.CompSetAccessPoliciesRouter
import com.keyway.application.router.compset.CompSetRouter
import com.keyway.application.router.health.HealthCheckRouter
import com.keyway.application.router.property.PropertyRouter
import com.keyway.application.utils.koin.createInstanceBy
import com.keyway.core.usecases.IdGenerator
import com.keyway.kommons.http.interceptors.CorrelationIdProvider
import com.keyway.kommons.http.interceptors.UUIDCorrelationIdProvider
import com.keyway.kommons.mapper.Mapper
import com.keyway.kommons.mapper.jackson.JacksonMapper
import com.keyway.security.domain.algorithm.AlgorithmRepository
import com.keyway.security.domain.token.TokenSdk
import com.keyway.security.infra.auth0.token.Auth0TokenSdk
import com.keyway.security.repository.algorithm.AlgorithmCacheRepository
import org.koin.core.module.Module
import org.koin.core.qualifier.named
import org.koin.dsl.module
import java.time.Clock
import java.util.UUID

object MainModule : KoinModule {
    override fun get(): Module =
        module(createdAtStart = true) {
            single { KtorApp.createApp(get(), get(), get(named(ROUTES))) }

            // Configuration
            single { ConfigParser.read() }
            single { get<Configuration>().system }
            single { get<Configuration>().datadogConfig }
            single { get<Configuration>().databaseConfig }
            single { get<Configuration>().awsConfig }
            single { get<Configuration>().sqsConfig }
            single { get<Configuration>().auth0TokenVerification }
            single { get<Configuration>().snsConfig }

            single { UrlJwkProvider(get<Auth0TokenVerificationConfig>().domain) }

            single<AlgorithmRepository> { AlgorithmCacheRepository(urlJwkProvider = get()) }

            single<CorrelationIdProvider> { UUIDCorrelationIdProvider() }

            single<TokenSdk> {
                Auth0TokenSdk(
                    issuer = get<Auth0TokenVerificationConfig>().issuer,
                    audience = listOf(get<Auth0TokenVerificationConfig>().audience),
                    algorithmRepository = get(),
                )
            }

            single<Clock> { Clock.systemUTC() }
            single<IdGenerator> { IdGenerator { UUID.randomUUID().toString() } }

            // Mappers
            single<Mapper>(named(ModuleConstants.APP_MAPPER)) { JacksonMapper(lowerCamelCaseObjectMapper) }

            // Handlers
            single { createInstanceBy(::HealthCheckHandler) }

            // COMPSET Handlers
            single { createInstanceBy(::CreateCompSetHandler) }
            single { GetAllCompSetsHandler(get(), get()) }
            single { createInstanceBy(::UpdateCompSetHandler) }
            single { createInstanceBy(::DeleteCompSetHandler) }
            single { createInstanceBy(::GetCompSetByIdHandler) }

            single { createInstanceBy(::CreateCompSetAccessPolicyHandler) }
            single { createInstanceBy(::DeleteCompSetAccessPolicyHandler) }
            single { createInstanceBy(::GetCompSetByIdAccessPolicyHandler) }
            single { createInstanceBy(::GetComSetPermissionGroupHandler) }

            // COMPS Handlers
            single { createInstanceBy(::GetPropertyComparableHandler) }
            single { createInstanceBy(::CompareCompSetPropertiesHandler) }
            single { createInstanceBy(::CompareTwoPropertiesHandler) }

            // PROPERTY Handlers
            single { createInstanceBy(::CreatePropertyHandler) }
            single { createInstanceBy(::GetPropertyByIdHandler) }

            // Routers
            single(named(ROUTES)) {
                setOf(
                    createInstanceBy(::HealthCheckRouter),
                    createInstanceBy(::CompSetRouter),
                    createInstanceBy(::ComparableRouter),
                    createInstanceBy(::CompSetAccessPoliciesRouter),
                    createInstanceBy(::PropertyRouter),
                )
            }
        }
}
