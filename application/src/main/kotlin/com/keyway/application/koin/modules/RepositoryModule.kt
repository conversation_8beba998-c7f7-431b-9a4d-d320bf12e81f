package com.keyway.application.koin.modules

import com.keyway.adapters.repositories.CompSetAccessPolicyPostgresRepository
import com.keyway.adapters.repositories.CompSetPostgresRepository
import com.keyway.adapters.repositories.ComparablePostgresRepository
import com.keyway.adapters.repositories.MultifamilyPropertyPostgresRepository
import com.keyway.adapters.repositories.PropertyPostgresRepository
import com.keyway.adapters.repositories.SimilarityConfigurationPostgresRepository
import com.keyway.core.ports.CompSetAccessPolicyRepository
import com.keyway.core.ports.CompSetRepository
import com.keyway.core.ports.ComparableRepository
import com.keyway.core.ports.MultifamilyPropertyRepository
import com.keyway.core.ports.PropertyRepository
import com.keyway.core.ports.SimilarityConfigurationRepository
import com.keyway.kommons.db.DataSourceMaker
import com.keyway.kommons.db.SqlClient
import org.koin.core.module.Module
import org.koin.dsl.module

object RepositoryModule : KoinModule {
    override fun get(): Module =
        module(createdAtStart = true) {
            single { DataSourceMaker.createDataSource(get()) }
            single { SqlClient(get()) }

            single<MultifamilyPropertyRepository> {
                MultifamilyPropertyPostgresRepository(get(), get())
            }

            single<CompSetRepository> {
                CompSetPostgresRepository(get())
            }

            single<ComparableRepository> {
                ComparablePostgresRepository(get())
            }

            single<SimilarityConfigurationRepository> {
                SimilarityConfigurationPostgresRepository(get())
            }

            single<CompSetAccessPolicyRepository> {
                CompSetAccessPolicyPostgresRepository(get())
            }

            single<PropertyRepository> {
                PropertyPostgresRepository(get(), get())
            }
        }
}
