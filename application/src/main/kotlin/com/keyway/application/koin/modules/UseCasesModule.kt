package com.keyway.application.koin.modules

import com.keyway.adapters.executor.BaseUseCaseExecutor
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.application.configuration.model.Configuration
import com.keyway.core.services.CompSetService
import com.keyway.core.usecases.compset.AddNewCompSet
import com.keyway.core.usecases.compset.DeleteCompSet
import com.keyway.core.usecases.compset.GetCompSet
import com.keyway.core.usecases.compset.GetCompSets
import com.keyway.core.usecases.compset.UpdateOrganizationCompSet
import com.keyway.core.usecases.compset.access.policy.CreateCompSetAccessPolicy
import com.keyway.core.usecases.compset.access.policy.DeleteCompSetAccessPolicy
import com.keyway.core.usecases.compset.access.policy.GetCompSetAccessPolicies
import com.keyway.core.usecases.compset.access.policy.GetCompSetPermissionGroup
import com.keyway.core.usecases.properties.CreateProperty
import com.keyway.core.usecases.properties.GetPropertyById
import com.keyway.core.usecases.properties.comparable.GetPropertyComparable
import com.keyway.core.usecases.properties.operational.SaveMultifamilyProperty
import com.keyway.core.usecases.properties.operational.SaveMultifamilyPropertyQualityScore
import com.keyway.core.usecases.properties.operational.UpdatePropertyLastSeen
import org.koin.core.module.Module
import org.koin.dsl.module

object UseCasesModule : KoinModule {
    override fun get(): Module =
        module(createdAtStart = true) {
            // Use Cases
            single<UseCaseExecutor> { BaseUseCaseExecutor }

            single { CompSetService(get(), get(), get()) }

            single { SaveMultifamilyProperty(get()) }
            single { UpdatePropertyLastSeen(get()) }
            single { SaveMultifamilyPropertyQualityScore(get()) }
            single { CreateProperty(get(), get(), get(), get()) }
            single { GetPropertyById(get()) }

            single {
                AddNewCompSet(
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get(),
                    get<Configuration>().userCompSetOnlyOrgIds,
                )
            }
            single { GetCompSets(get(), get()) }
            single { GetCompSet(get()) }
            single { UpdateOrganizationCompSet(get(), get(), get(), get()) }
            single { DeleteCompSet(get(), get(), get(), get()) }

            single { GetPropertyComparable(get(), get(), get(), get()) }

            single { CreateCompSetAccessPolicy(get(), get(), get(), get()) }
            single { DeleteCompSetAccessPolicy(get()) }
            single { GetCompSetAccessPolicies(get()) }
            single { GetCompSetPermissionGroup(get(), get()) }
        }
}
