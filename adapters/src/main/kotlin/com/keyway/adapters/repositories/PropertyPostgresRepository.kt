package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.models.PropertyModel
import com.keyway.core.entities.multifamily.BaseMultifamilyProperty
import com.keyway.core.ports.PropertyRepository
import com.keyway.kommons.db.SqlClient
import java.time.Clock

class PropertyPostgresRepository(
    private val sqlClient: SqlClient,
    private val clock: Clock,
) : PropertyRepository {
    override fun saveOrUpdate(property: BaseMultifamilyProperty) {
        sqlClient.update(
            insertProperty,
        ) { ps ->
            ps.setString(1, property.id)
            ps.setString(2, property.name)
            ps.setString(3, property.address)
            ps.setString(4, property.city)
            ps.setString(5, property.state)
            ps.setString(6, property.zipCode)
            ps.setBigDecimal(7, property.location.latitude)
            ps.setBigDecimal(8, property.location.longitude)
            ps.setBigDecimal(9, property.location.longitude)
            ps.setBigDecimal(10, property.location.latitude)
            ps.setArray(11, ps.connection.createArrayOf("VARCHAR", property.propertyAmenities?.map { it.name }?.toTypedArray() ?: emptyArray()))
            ps.setArray(12, ps.connection.createArrayOf("VARCHAR", property.unitsAmenities?.map { it.name }?.toTypedArray() ?: emptyArray()))
            ps.setArray(13, ps.connection.createArrayOf("VARCHAR", property.housingSegments?.map { it.name }?.toTypedArray() ?: emptyArray()))
            ps.setString(14, property.propertyStyle?.name)
            ps.setInt(15, property.unitsQuantity)
            ps.setObject(16, property.constructionYear)
            ps.setString(17, property.stage.name)
            ps.setString(18, property.organizationId)
            ps.setString(19, property.createdBy)
            ps.setObject(20, property.createdAt)
            ps.setObject(21, property.updatedAt)
            ps.setObject(22, property.stories)
        }
    }

    override fun getById(id: String): BaseMultifamilyProperty =
        sqlClient
            .getOneOrFail(
                query = "SELECT * FROM PROPERTIES WHERE ID = ?",
                params = listOf(id),
                messageKey = "PROPERTY NOT FOUND",
                clazz = PropertyModel::class.java,
            ).toBaseMultifamilyProperty()

    override fun getByOrganizationId(organizationId: String): List<BaseMultifamilyProperty> =
        sqlClient
            .getAll(
                query = "SELECT * FROM PROPERTIES WHERE ORGANIZATION_ID = ? ORDER BY CREATED_AT DESC",
                params = listOf(organizationId),
                clazz = PropertyModel::class.java,
            ).map(PropertyModel::toBaseMultifamilyProperty)

    private val insertProperty =
        """
        INSERT INTO PROPERTIES (
            ID, 
            NAME, 
            ADDRESS, 
            CITY, 
            STATE, 
            ZIP_CODE,
            LATITUDE, 
            LONGITUDE,
            GEOLOCATION,
            PROPERTY_AMENITIES,
            UNITS_AMENITIES,
            HOUSING_SEGMENTS,
            PROPERTY_STYLE,
            UNITS_QUANTITY,
            CONSTRUCTION_YEAR,
            STAGE,
            ORGANIZATION_ID,
            CREATED_BY,
            CREATED_AT,
            UPDATED_AT,
            STORIES
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?,
            ST_SetSRID(ST_MakePoint(?, ?), 4326)::geography, 
            ?, ?, ?, ?, ?, 
            ?, ?, ?, ?, ?, ?, ?
        ) 
        ON CONFLICT (ID) DO UPDATE SET
            NAME = EXCLUDED.NAME,
            ADDRESS = EXCLUDED.ADDRESS,
            CITY = EXCLUDED.CITY,
            STATE = EXCLUDED.STATE,
            ZIP_CODE = EXCLUDED.ZIP_CODE,
            LATITUDE = EXCLUDED.LATITUDE,
            LONGITUDE = EXCLUDED.LONGITUDE,
            GEOLOCATION = EXCLUDED.GEOLOCATION,
            PROPERTY_AMENITIES = EXCLUDED.PROPERTY_AMENITIES,
            UNITS_AMENITIES = EXCLUDED.UNITS_AMENITIES,
            HOUSING_SEGMENTS = EXCLUDED.HOUSING_SEGMENTS,
            PROPERTY_STYLE = EXCLUDED.PROPERTY_STYLE,
            UNITS_QUANTITY = EXCLUDED.UNITS_QUANTITY,
            CONSTRUCTION_YEAR = EXCLUDED.CONSTRUCTION_YEAR,
            STAGE = EXCLUDED.STAGE,
            ORGANIZATION_ID = EXCLUDED.ORGANIZATION_ID,
            CREATED_BY = EXCLUDED.CREATED_BY,
            UPDATED_AT = EXCLUDED.UPDATED_AT,
            STORIES = EXCLUDED.STORIES;
        """.trimIndent()
}
