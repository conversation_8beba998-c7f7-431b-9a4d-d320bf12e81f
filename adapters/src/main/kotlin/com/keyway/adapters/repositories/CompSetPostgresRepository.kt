package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.models.CompSetModel
import com.keyway.core.entities.AccessEntityType
import com.keyway.core.entities.CompSet
import com.keyway.core.ports.CompSetRepository
import com.keyway.kommons.db.SqlClient
import com.keyway.kommons.db.mapper.DatabaseMapper
import org.postgresql.util.PGobject
import java.sql.PreparedStatement
import java.sql.Types
import java.time.LocalDate
import kotlin.collections.List

class CompSetPostgresRepository(
    private val sqlClient: SqlClient,
) : CompSetRepository {
    override fun getCompSetsWithAccess(
        organizationId: String,
        userId: String,
    ): List<CompSet> =
        sqlClient
            .getAll(
                """WITH permission_arrays AS (
                    SELECT
                        cs.id as comp_set_id,
                        array_agg(DISTINCT p) as permissions
                    FROM
                        comp_set cs
                    INNER JOIN comp_set_access_policies csap ON cs.id = csap.comp_set_id
                    CROSS JOIN LATERAL unnest(csap.permissions) as p
                    WHERE
                        (csap.entity_id = ? AND csap.entity_type = '${AccessEntityType.ORGANIZATION.name}')
                        OR (csap.entity_id = ? AND csap.entity_type = '${AccessEntityType.USER.name}')
                    GROUP BY cs.id
                )
                SELECT cs.*, pa.permissions
                FROM comp_set cs
                INNER JOIN permission_arrays pa ON cs.id = pa.comp_set_id
                """.trimMargin(),
                params = listOfNotNull(organizationId, userId),
                clazz = CompSetModel::class.java,
            ).map(CompSetModel::toCompSet)

    override fun getAllCompSets(
        organizationId: String,
        userId: String?,
    ): List<CompSet> =
        sqlClient
            .getAll(
                """SELECT * FROM comp_set 
                    WHERE 
                    organization_id = ?  
                    ${" AND created_by = ? ".takeIf { userId != null } ?: ""}
                    
                """.trimMargin(),
                params = listOfNotNull(organizationId, userId),
                clazz = CompSetModel::class.java,
            ).map(CompSetModel::toCompSet)

    override fun getCompSetById(id: String): CompSet =
        sqlClient
            .getOneOrFail(
                "SELECT * FROM comp_set where id = ?",
                params = listOf(id),
                clazz = CompSetModel::class.java,
                messageKey = "MISSING COMPSET",
            ).toCompSet()

    override fun insertOrUpdate(compSet: CompSet) {
        sqlClient.update(
            """INSERT INTO comp_set (id, name, property_id, comps_ids, organization_id, 
                created_at, updated_at, created_by, base_property_acquisition_date, base_property_stage)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ON CONFLICT ON CONSTRAINT comp_set_PK_ID DO UPDATE SET
                name = EXCLUDED.name,
                comps_ids = EXCLUDED.comps_ids,
                updated_at = EXCLUDED.updated_at,
                base_property_acquisition_date = EXCLUDED.base_property_acquisition_date,
                base_property_stage = EXCLUDED.base_property_stage;
                """,
        ) { ps ->
            ps.setString(1, compSet.id)
            ps.setString(2, compSet.name)
            ps.setString(3, compSet.basePropertyId)
            ps.setObject(4, compSet.propertyIds.toPGObject())
            ps.setString(5, compSet.organizationId)
            ps.setObject(6, compSet.createdAt)
            ps.setObject(7, compSet.updatedAt)
            ps.setNullableString(8, compSet.createdBy)
            ps.setNullableLocalDate(9, compSet.basePropertyAcquisitionDate)
            ps.setString(10, compSet.basePropertyStage.name)
        }
    }

    override fun deleteCompSet(id: String) {
        sqlClient.update(
            query = "DELETE FROM comp_set WHERE id = ?",
            params = listOf(id),
        )
    }

    private fun LinkedHashSet<String>.toPGObject(): PGobject =
        this.let { values ->
            PGobject().apply {
                this.type = "jsonb"
                this.value = DatabaseMapper.encode(values)
            }
        }

    private fun PreparedStatement.setNullableString(
        parameterIndex: Int,
        value: String?,
    ) {
        value
            ?.also { setString(parameterIndex, value) }
            ?: setNull(parameterIndex, Types.VARCHAR)
    }

    private fun PreparedStatement.setNullableLocalDate(
        parameterIndex: Int,
        value: LocalDate?,
    ) {
        value
            ?.also { setObject(parameterIndex, value, Types.DATE) }
            ?: setNull(parameterIndex, Types.DATE)
    }
}
