package com.keyway.adapters.repositories.models

import com.fasterxml.jackson.databind.JsonNode
import com.keyway.core.entities.GeoPoint
import com.keyway.core.entities.multifamily.BaseMultifamilyProperty
import com.keyway.core.entities.multifamily.MultifamilyHousingSegment
import com.keyway.core.entities.multifamily.MultifamilyPropertyAmenity
import com.keyway.core.entities.multifamily.MultifamilyPropertyStyle
import com.keyway.core.entities.multifamily.MultifamilyUnitAmenity
import com.keyway.core.entities.multifamily.PropertyStage
import java.math.BigDecimal
import java.time.OffsetDateTime

data class PropertyModel(
    val id: String,
    val name: String,
    val address: String,
    val city: String,
    val state: String,
    val zipCode: String,
    val latitude: BigDecimal,
    val longitude: BigDecimal,
    val geolocation: JsonNode,
    val propertyAmenities: Set<String>? = emptySet(),
    val unitsAmenities: Set<String>? = emptySet(),
    val housingSegments: Set<String>? = emptySet(),
    val propertyStyle: String?,
    val unitsQuantity: Int,
    val constructionYear: Int?,
    val stage: String,
    val organizationId: String,
    val createdBy: String?,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime,
    val stories: Int?,
) {
    fun toBaseMultifamilyProperty(): BaseMultifamilyProperty =
        BaseMultifamilyProperty(
            id = this.id,
            name = this.name,
            address = this.address,
            city = this.city,
            state = this.state,
            zipCode = this.zipCode,
            location = GeoPoint(latitude, longitude),
            geolocation = this.geolocation["value"].asText(),
            propertyAmenities =
                this.propertyAmenities?.map { amenityName ->
                    MultifamilyPropertyAmenity.valueOf(amenityName)
                } ?: emptyList(),
            unitsAmenities =
                this.unitsAmenities?.map { amenityName ->
                    MultifamilyUnitAmenity.valueOf(amenityName)
                } ?: emptyList(),
            housingSegments =
                this.housingSegments?.map { segmentName ->
                    MultifamilyHousingSegment.valueOf(segmentName)
                } ?: emptyList(),
            propertyStyle =
                this.propertyStyle?.let { styleName ->
                    MultifamilyPropertyStyle.valueOf(styleName)
                },
            unitsQuantity = this.unitsQuantity,
            constructionYear = this.constructionYear,
            stage =
                this.stage.let { stageName ->
                    PropertyStage.valueOf(stageName)
                },
            organizationId = this.organizationId,
            createdBy = this.createdBy,
            createdAt = this.createdAt,
            updatedAt = this.updatedAt,
            stories = this.stories,
        )
}
