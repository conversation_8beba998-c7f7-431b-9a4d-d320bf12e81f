package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.extensions.addPagination
import com.keyway.adapters.repositories.extensions.getQueryFiltersAndParams
import com.keyway.adapters.repositories.similarity.queries.PropertySimilarityQueryBuilder
import com.keyway.core.dto.PropertyCompsResult
import com.keyway.core.entities.PropertySimilarityInput
import com.keyway.core.entities.ScoreDetail
import com.keyway.core.entities.ScoredMultifamilyProperty
import com.keyway.core.entities.SimilarityScoreType
import com.keyway.core.entities.SimilarityWeight
import com.keyway.core.entities.UnitMix
import com.keyway.core.entities.search.NumberOperator
import com.keyway.core.entities.search.PropertyCompsPaginatedInput
import com.keyway.core.entities.search.SearchFieldParam
import com.keyway.core.entities.search.SearchNumber
import com.keyway.core.ports.ComparableRepository
import com.keyway.core.utils.BigDecimalUtils
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.math.BigDecimal

class ComparablePostgresRepository(
    private val sqlClient: SqlClient,
) : ComparableRepository {
    companion object {
        private val MILES_MULTIPLIER = BigDecimal("1609.34")
    }

    override suspend fun getPropertyComps(
        property: PropertySimilarityInput,
        similarityWeights: List<SimilarityWeight>,
        paginatedInput: PropertyCompsPaginatedInput,
        propertyIds: Set<String>,
    ): PropertyCompsResult =
        withContext(Dispatchers.IO) {
            val score =
                PropertySimilarityQueryBuilder(
                    property,
                    similarityWeights,
                )
            val hasUnitMix = score.finalTerms.any { it.type == SimilarityScoreType.UNIT_MIX }
            val distanceFilter =
                getDistanceFilter(paginatedInput, property).takeIf {
                    propertyIds.isEmpty()
                }
            val filters = paginatedInput.filters.getQueryFiltersAndParams()

            val query =
                """
                SELECT 
                   scored_props.*,
                   ${score.finalScoreQuery} as score
                FROM (
                   SELECT
                       mf.*,
                       ${score.query}
                   FROM base_prop_data mf
                   ${"".takeUnless { hasUnitMix } ?: "LEFT JOIN unit_mix_data um ON mf.id = um.prop_id"} 
                ) 
                   as scored_props
                """.trimIndent()

            val finalQuery =
                """
                    WITH base_prop_data AS (
                    SELECT
                        mf_prop.id,
                        ROUND(ST_Distance((?)::geography, (mf_prop.geolocation)::geography)::numeric / $MILES_MULTIPLIER, 2) as distance,
                        ${getSelectFields(score, paginatedInput)}
                    FROM multifamily_properties mf_prop
                    WHERE mf_prop.id != ? 
                    AND mf_prop.is_active = true 
                    ${distanceFilter?.let { " AND $it " } ?: ""}
                    ${propertyIds.propsIdsWhere("mf_prop")} )
                ${
                    "".takeUnless { hasUnitMix }
                        ?: unitMixSubQuery(
                            property.units,
                            BigDecimal(property.unitQuantity),
                        )
                } 
                
                ${
                    query.addPagination(
                        size = paginatedInput.size,
                        offset = paginatedInput.offset,
                        orderBy = paginatedInput.orderBy,
                        order = paginatedInput.order.name,
                        where = filters.first,
                        withTotalCount = paginatedInput.calculateTotal,
                    )
                }
                """.trimIndent()

            val params =
                listOf(
                    property.geolocation,
                ).plus(
                    property.id,
                ).plus(
                    score.params,
                ).plus(
                    filters.second,
                )
            var total: Int? = 0
            val scoredComps =
                sqlClient.getAll(finalQuery, params) { row ->
                    ScoredMultifamilyProperty(
                        propertyId = row["id"].toString(),
                        distance = row.getBigDecimal("distance"),
                        score = row.getBigDecimal("score"),
                        scoreDetail = scoreDetails(score.finalTerms, row),
                    ).also {
                        total = row["total_count"].toString().toInt().takeIf { it >= 0 }
                    }
                }

            PropertyCompsResult(
                total = total,
                comps = scoredComps,
            )
        }

    private fun getSelectFields(
        score: PropertySimilarityQueryBuilder,
        paginatedInput: PropertyCompsPaginatedInput,
    ) = score.finalTerms
        .asSequence()
        .flatMap { it.requiredFields }
        .plus(paginatedInput.filters.keys)
        .mapNotNull {
            it.trim().let { field ->
                when (field) {
                    "has_affordable_units" -> "coalesce(mf_prop.has_affordable_units, false) as has_affordable_units"
                    "distance" -> null
                    "id" -> null
                    "score" -> null
                    else -> "mf_prop.$field"
                }
            }
        }.toSet()
        .joinToString(",") { it }

    private fun getDistanceFilter(
        paginatedInput: PropertyCompsPaginatedInput,
        property: PropertySimilarityInput,
    ): String? =
        paginatedInput.filters["distance"]
            ?.let { filter: SearchFieldParam ->
                (filter as SearchNumber<BigDecimal>).getParamValues().let { values ->
                    (values as List<BigDecimal>)
                        .first()
                        .takeUnless { listOf(NumberOperator.BETWEEN).contains(filter.operator) } ?: values[1]
                }
            }?.let {
                """ ST_DWITHIN ((mf_prop.GEOLOCATION)::GEOGRAPHY, 
               '${property.geolocation}'::GEOGRAPHY, 
               ${MILES_MULTIPLIER.multiply(it)} )""".trim()
            }

    private fun scoreDetails(
        scoreTerms: List<SimilarityWeight>,
        row: Map<String, Any>,
    ) = scoreTerms.map { term ->
        ScoreDetail(
            type = term.type,
            weight = row.getBigDecimal(term.weightColumnName()),
            score = row.getBigDecimal(term.columnName()),
        )
    }

    private fun Map<String, Any>.getBigDecimal(name: String) = this[name].toString().toBigDecimal(BigDecimalUtils.mathContext)

    private fun unitMixSubQuery(
        units: List<UnitMix>,
        unitPropertyQuantity: BigDecimal,
    ): String =
        """
              , unit_mix_data AS (
              SELECT
                  prop_id,
                  sum(score) / count(prop_id) as score
              FROM (SELECT mf_prop.id                                      as prop_id,
                           units.bedrooms,
                           similarity_score(1 - ((units.quantity::numeric / mf_prop.unit_quantity::numeric)
                               / property_unit_data.percentage), 0.5) as score
                    FROM BASE_PROP_DATA mf_prop
                             JOIN multifamily_property_units units
                                  ON mf_prop.id = units.property_id
                             JOIN (${
            units.joinToString(" UNION ALL ") {
                """ SELECT ${it.bedrooms} as beds,
                    ${BigDecimal(it.quantity).divide(unitPropertyQuantity, BigDecimalUtils.mathContext)} as percentage 
                """.trimMargin()
            }
        }) as property_unit_data on units.bedrooms = property_unit_data.beds 
        WHERE units.bedrooms in (${units.joinToString(",") { it.bedrooms.toString() } })
                         ) as gruped_unit_mix
              GROUP BY prop_id
              )
        """.trimIndent()

    private fun Set<String>.propsIdsWhere(
        tableAlias: String,
        column: String = "id",
    ) = this.takeIf { it.isNotEmpty() }?.let { ids ->
        """ AND $tableAlias.$column IN ( ${ ids.joinToString(",") { "'$it'" }} ) """
    } ?: ""
}
