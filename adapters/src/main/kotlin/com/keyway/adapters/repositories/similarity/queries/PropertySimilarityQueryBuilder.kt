package com.keyway.adapters.repositories.similarity.queries

import com.keyway.core.entities.PropertySimilarityInput
import com.keyway.core.entities.SimilarityScoreType
import com.keyway.core.entities.SimilarityWeight
import java.math.BigDecimal

class PropertySimilarityQueryBuilder(
    input: PropertySimilarityInput,
    terms: List<SimilarityWeight>,
) {
    var query: String
    var params: List<Any>
    var finalScoreQuery: String
    var finalTerms: List<SimilarityWeight>

    init {
        finalTerms = filterTerms(terms, input)
        query = buildQuery(finalTerms)
        params = buildParams(finalTerms, input)
        finalScoreQuery = buildFinalScoreQuery(finalTerms)
    }

    private fun buildFinalScoreQuery(terms: List<SimilarityWeight>): String =
        """
        ROUND(${terms.joinToString(separator = " + ")
            { """ (${it.columnName()} * ${it.weightColumnName()}) """} }
        , 2)
        """.trimIndent()

    private fun filterTerms(
        terms: List<SimilarityWeight>,
        input: PropertySimilarityInput,
    ): List<SimilarityWeight> =
        terms.filter { term ->
            when (term.type) {
                SimilarityScoreType.SQUARE_FOOTAGE_PER_UNIT ->
                    (input.squareFootagePerUnit ?: BigDecimal.ZERO) > BigDecimal.ZERO
                SimilarityScoreType.CONSTRUCTION_YEAR -> input.constructionYear != null
                SimilarityScoreType.UNIT_AMOUNT -> input.unitQuantity > 0
                SimilarityScoreType.UNIT_MIX -> input.units.isNotEmpty()
                SimilarityScoreType.PROPERTY_AMENITIES -> input.propertyAmenities.isNotEmpty()
                SimilarityScoreType.UNITS_AMENITIES -> input.unitsAmenities.isNotEmpty()
                SimilarityScoreType.QUALITY -> input.qualityOverallScore != null
                SimilarityScoreType.STYLE -> input.propertyStyle != null
                SimilarityScoreType.STORIES -> input.stories != null
                else -> true
            }
        }

    private fun buildParams(
        terms: List<SimilarityWeight>,
        input: PropertySimilarityInput,
    ): MutableList<Any> {
        val params = mutableListOf<Any>()
        terms
            .mapNotNull { term ->
                when (term.type) {
                    SimilarityScoreType.DISTANCE -> null
                    SimilarityScoreType.SQUARE_FOOTAGE_PER_UNIT ->
                        listOfNotNull(
                            input.squareFootagePerUnit ?: BigDecimal.ONE,
                        )
                    SimilarityScoreType.CONSTRUCTION_YEAR ->
                        listOfNotNull(
                            input.constructionYear,
                        )
                    SimilarityScoreType.UNIT_AMOUNT ->
                        listOfNotNull(
                            input.unitQuantity,
                        )
                    SimilarityScoreType.SALE_DATE -> null
                    SimilarityScoreType.UNIT_MIX -> null
                    SimilarityScoreType.PROPERTY_AMENITIES -> listOfNotNull(input.propertyAmenities)
                    SimilarityScoreType.UNITS_AMENITIES -> listOfNotNull(input.unitsAmenities)
                    SimilarityScoreType.QUALITY -> listOfNotNull(input.qualityOverallScore)
                    SimilarityScoreType.STYLE -> listOfNotNull(input.propertyStyle)
                    SimilarityScoreType.STORIES -> listOfNotNull(input.stories)
                }
            }.forEach { params.addAll(it) }
        return params
    }

    private fun buildQuery(terms: List<SimilarityWeight>) = terms.termsQuery()

    private fun List<SimilarityWeight>.termsQuery() =
        this
            .sumOf { it.weight }
            .let { totalWeight ->
                this.joinToString(separator = " , ") {
                    """
                    round(coalesce(${it.query}, 0), 2)::numeric as ${it.columnName()},
                    (${it.weight / totalWeight})::numeric as ${it.weightColumnName()}
                    """.trimIndent()
                }
            }
}
