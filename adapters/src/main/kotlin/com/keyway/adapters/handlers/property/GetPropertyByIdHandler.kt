package com.keyway.adapters.handlers.property

import com.keyway.adapters.dtos.property.MultifamilyProperty
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.usecases.properties.GetPropertyById
import com.keyway.kommons.mapper.dataclass.mapTo

class GetPropertyByIdHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getPropertyById: GetPropertyById,
) {
    suspend operator fun invoke(
        propertyId: String,
        organizationId: String,
    ): MultifamilyProperty =
        useCaseExecutor(
            useCase = getPropertyById,
            inputDto = GetPropertyById.Input(propertyId, organizationId),
            inputConverter = { it },
            outputConverter = { it.mapTo() },
        )
}
