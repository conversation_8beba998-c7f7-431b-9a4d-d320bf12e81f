package com.keyway.adapters.handlers.property

import com.keyway.adapters.dtos.property.CreatePropertyInput
import com.keyway.adapters.dtos.property.MultifamilyProperty
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.kommons.mapper.dataclass.mapTo
import com.keyway.core.usecases.properties.CreateProperty as CreatePropertyUseCase

class CreatePropertyHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val createProperty: CreatePropertyUseCase,
) {
    suspend operator fun invoke(
        input: CreatePropertyInput,
        organizationId: String,
        token: String,
    ): MultifamilyProperty =
        useCaseExecutor(
            useCase = createProperty,
            inputDto = Triple(input, organizationId, token),
            inputConverter = { (inputDto, orgId, token) ->
                inputDto.mapTo(
                    additions =
                        mapOf(
                            "organizationId" to orgId,
                            "token" to token,
                        ),
                )
            },
            outputConverter = { it.mapTo() },
        )
}
