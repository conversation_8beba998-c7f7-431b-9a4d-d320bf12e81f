package com.keyway.adapters.dtos.property

import com.keyway.core.entities.GeoPoint
import com.keyway.core.entities.multifamily.MultifamilyHousingSegment
import com.keyway.core.entities.multifamily.MultifamilyPropertyAmenity
import com.keyway.core.entities.multifamily.MultifamilyPropertyStyle
import com.keyway.core.entities.multifamily.MultifamilyUnitAmenity
import com.keyway.core.entities.multifamily.PropertyStage

data class MultifamilyProperty(
    val id: String,
    val name: String,
    val address: String,
    val city: String,
    val state: String,
    val zipCode: String,
    val location: GeoPoint,
    val propertyAmenities: List<MultifamilyPropertyAmenity>? = listOf(),
    val unitsAmenities: List<MultifamilyUnitAmenity>? = listOf(),
    val housingSegments: List<MultifamilyHousingSegment>? = listOf(),
    val propertyStyle: MultifamilyPropertyStyle? = null,
    val unitsQuantity: Int,
    val constructionYear: Int? = null,
    val stage: PropertyStage,
    val stories: Int? = null,
)
