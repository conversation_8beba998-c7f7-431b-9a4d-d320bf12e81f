package com.keyway.adapters.dtos.compset

import com.keyway.core.entities.multifamily.PropertyStage
import com.keyway.core.ports.CompSetEventType
import java.time.OffsetDateTime

data class ComparablePropertySetEvent(
    val id: String,
    val name: String,
    val basePropertyId: String,
    val propertyIds: LinkedHashSet<String>,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime,
    val eventType: CompSetEventType,
    val createdBy: String?,
    val organizationId: String,
    val basePropertyStage: PropertyStage,
)
