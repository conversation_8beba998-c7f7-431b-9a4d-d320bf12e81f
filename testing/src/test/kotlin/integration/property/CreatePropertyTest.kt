package integration.property

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.property.CreatePropertyInput
import com.keyway.adapters.dtos.property.MultifamilyProperty
import com.keyway.core.entities.GeoPoint
import com.keyway.core.entities.multifamily.MultifamilyHousingSegment
import com.keyway.core.entities.multifamily.MultifamilyPropertyAmenity
import com.keyway.core.entities.multifamily.MultifamilyPropertyStyle
import com.keyway.core.entities.multifamily.MultifamilyUnitAmenity
import com.keyway.core.entities.multifamily.PropertyStage
import com.keyway.core.ports.PropertyRepository
import com.keyway.kommons.mapper.JsonMapper
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import java.math.BigDecimal

class CreatePropertyTest : BaseApplicationTest() {
    private val propertyRepository: PropertyRepository by inject()
    private lateinit var token: String
    private val baseUrl = "${localUrl()}/properties"

    @BeforeEach
    fun setUp() {
        token = authHeaderValue()
    }

    @Test
    fun `should create property with all fields and save correctly`() {
        // Given
        val input =
            CreatePropertyInput(
                name = "Test Property",
                address = "123 Main St",
                city = "Austin",
                state = "TX",
                zipCode = "78701",
                location =
                    GeoPoint(
                        latitude = BigDecimal("30.2672"),
                        longitude = BigDecimal("-97.7431"),
                    ),
                propertyAmenities =
                    listOf(
                        MultifamilyPropertyAmenity.SWIMMING_POOL,
                        MultifamilyPropertyAmenity.FITNESS_CENTER,
                        MultifamilyPropertyAmenity.PARKING_GARAGE,
                    ),
                unitsAmenities =
                    listOf(
                        MultifamilyUnitAmenity.AIR_CONDITIONING,
                        MultifamilyUnitAmenity.HARDWOOD_FLOOR,
                        MultifamilyUnitAmenity.DISHWASHER,
                    ),
                housingSegments =
                    listOf(
                        MultifamilyHousingSegment.CONVENTIONAL,
                        MultifamilyHousingSegment.STUDENT,
                    ),
                propertyStyle = MultifamilyPropertyStyle.MID_RISE,
                unitsQuantity = 150,
                constructionYear = 2020,
                stories = 5,
            )

        // When
        val result =
            Unirest
                .post(baseUrl)
                .headers(mapOf(HttpHeaders.Authorization to token))
                .body(JsonMapper.encode(input))
                .asString()

        // Then
        assertEquals(HttpStatusCode.Created.value, result.status)

        val createdProperty = JsonMapper.decode(result.body, MultifamilyProperty::class.java)

        // Verify response structure
        assertNotNull(createdProperty.id)
        assertEquals(input.name, createdProperty.name)
        assertEquals(input.address, createdProperty.address)
        assertEquals(input.city, createdProperty.city)
        assertEquals(input.state, createdProperty.state)
        assertEquals(input.zipCode, createdProperty.zipCode)
        assertEquals(input.location, createdProperty.location)
        assertEquals(input.unitsQuantity, createdProperty.unitsQuantity)
        assertEquals(input.constructionYear, createdProperty.constructionYear)
        assertEquals(PropertyStage.DEVELOPING, createdProperty.stage)

        // Verify amenities
        assertEquals(3, createdProperty.propertyAmenities?.size)
        assertTrue(createdProperty.propertyAmenities?.contains(MultifamilyPropertyAmenity.SWIMMING_POOL) == true)
        assertTrue(createdProperty.propertyAmenities?.contains(MultifamilyPropertyAmenity.FITNESS_CENTER) == true)
        assertTrue(createdProperty.propertyAmenities?.contains(MultifamilyPropertyAmenity.PARKING_GARAGE) == true)

        assertEquals(3, createdProperty.unitsAmenities?.size)
        assertTrue(createdProperty.unitsAmenities?.contains(MultifamilyUnitAmenity.AIR_CONDITIONING) == true)
        assertTrue(createdProperty.unitsAmenities?.contains(MultifamilyUnitAmenity.HARDWOOD_FLOOR) == true)
        assertTrue(createdProperty.unitsAmenities?.contains(MultifamilyUnitAmenity.DISHWASHER) == true)

        // Verify housing segments
        assertEquals(2, createdProperty.housingSegments?.size)
        assertTrue(createdProperty.housingSegments?.contains(MultifamilyHousingSegment.CONVENTIONAL) == true)
        assertTrue(createdProperty.housingSegments?.contains(MultifamilyHousingSegment.STUDENT) == true)

        // Verify property style
        assertEquals(MultifamilyPropertyStyle.MID_RISE, createdProperty.propertyStyle)

        // Verify data is saved in database
        val savedProperty = propertyRepository.getById(createdProperty.id)
        assertEquals(createdProperty.id, savedProperty.id)
        assertEquals(createdProperty.name, savedProperty.name)
        assertEquals(createdProperty.location, savedProperty.location)
        assertEquals(createdProperty.unitsQuantity, savedProperty.unitsQuantity)
        assertEquals(createdProperty.constructionYear, savedProperty.constructionYear)
    }

    @Test
    fun `should create property with minimal required fields`() {
        // Given
        val input =
            CreatePropertyInput(
                name = "Minimal Property",
                address = "456 Oak Ave",
                city = "Dallas",
                state = "TX",
                zipCode = "75201",
                location =
                    GeoPoint(
                        latitude = BigDecimal("32.7767"),
                        longitude = BigDecimal("-96.7970"),
                    ),
                unitsQuantity = 50,
            )

        // When
        val result =
            Unirest
                .post(baseUrl)
                .headers(mapOf(HttpHeaders.Authorization to token))
                .body(JsonMapper.encode(input))
                .asString()

        // Then
        assertEquals(HttpStatusCode.Created.value, result.status)

        val createdProperty = JsonMapper.decode(result.body, MultifamilyProperty::class.java)

        // Verify response structure
        assertNotNull(createdProperty.id)
        assertEquals(input.name, createdProperty.name)
        assertEquals(input.address, createdProperty.address)
        assertEquals(input.city, createdProperty.city)
        assertEquals(input.state, createdProperty.state)
        assertEquals(input.zipCode, createdProperty.zipCode)
        assertEquals(input.location, createdProperty.location)
        assertEquals(input.unitsQuantity, createdProperty.unitsQuantity)
        assertEquals(PropertyStage.DEVELOPING, createdProperty.stage)

        // Verify optional fields are empty/null
        assertTrue(createdProperty.propertyAmenities?.isEmpty() == true)
        assertTrue(createdProperty.unitsAmenities?.isEmpty() == true)
        assertTrue(createdProperty.housingSegments?.isEmpty() == true)
        assertEquals(null, createdProperty.propertyStyle)
        assertEquals(null, createdProperty.constructionYear)

        // Verify data is saved in database
        val savedProperty = propertyRepository.getById(createdProperty.id)
        assertEquals(createdProperty.id, savedProperty.id)
        assertEquals(createdProperty.name, savedProperty.name)
    }

    @Test
    fun `should return 401 when no authentication token provided`() {
        // Given
        val input =
            CreatePropertyInput(
                name = "Test Property",
                address = "123 Main St",
                city = "Austin",
                state = "TX",
                zipCode = "78701",
                location =
                    GeoPoint(
                        latitude = BigDecimal("30.2672"),
                        longitude = BigDecimal("-97.7431"),
                    ),
                unitsQuantity = 100,
            )

        // When
        val result =
            Unirest
                .post(baseUrl)
                .body(JsonMapper.encode(input))
                .asString()

        // Then
        assertEquals(HttpStatusCode.Unauthorized.value, result.status)
    }

    @Test
    fun `should return 400 when required fields are missing`() {
        // Given - missing required field 'name'
        val invalidInput =
            mapOf(
                "address" to "123 Main St",
                "city" to "Austin",
                "state" to "TX",
                "zipCode" to "78701",
                "location" to
                    mapOf(
                        "latitude" to "30.2672",
                        "longitude" to "-97.7431",
                    ),
                "unitsQuantity" to 100,
            )

        // When
        val result =
            Unirest
                .post(baseUrl)
                .headers(mapOf(HttpHeaders.Authorization to token))
                .body(JsonMapper.encode(invalidInput))
                .asString()

        // Then
        assertEquals(HttpStatusCode.BadRequest.value, result.status)
    }

    @Test
    fun `should handle properties with same organization correctly`() {
        // Given
        val input1 =
            CreatePropertyInput(
                name = "Property 1",
                address = "123 First St",
                city = "Austin",
                state = "TX",
                zipCode = "78701",
                location =
                    GeoPoint(
                        latitude = BigDecimal("30.2672"),
                        longitude = BigDecimal("-97.7431"),
                    ),
                unitsQuantity = 100,
            )

        val input2 =
            CreatePropertyInput(
                name = "Property 2",
                address = "456 Second St",
                city = "Austin",
                state = "TX",
                zipCode = "78702",
                location =
                    GeoPoint(
                        latitude = BigDecimal("30.2700"),
                        longitude = BigDecimal("-97.7400"),
                    ),
                unitsQuantity = 200,
            )

        // When
        val result1 =
            Unirest
                .post(baseUrl)
                .headers(mapOf(HttpHeaders.Authorization to token))
                .body(JsonMapper.encode(input1))
                .asString()

        val result2 =
            Unirest
                .post(baseUrl)
                .headers(mapOf(HttpHeaders.Authorization to token))
                .body(JsonMapper.encode(input2))
                .asString()

        // Then
        assertEquals(HttpStatusCode.Created.value, result1.status)
        assertEquals(HttpStatusCode.Created.value, result2.status)

        val property1 = JsonMapper.decode(result1.body, MultifamilyProperty::class.java)
        val property2 = JsonMapper.decode(result2.body, MultifamilyProperty::class.java)

        // Verify both properties have same organization but different IDs
        assertTrue(property1.id != property2.id)

        // Verify both are saved in database
        val savedProperty1 = propertyRepository.getById(property1.id)
        val savedProperty2 = propertyRepository.getById(property2.id)

        assertEquals(property1.name, savedProperty1.name)
        assertEquals(property2.name, savedProperty2.name)
    }
}
