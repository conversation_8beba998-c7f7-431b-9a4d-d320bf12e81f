package adapters.repository

import application.utils.anyString
import application.utils.base.BaseApplicationTest
import com.keyway.core.entities.CompSet
import com.keyway.core.entities.multifamily.PropertyStage
import com.keyway.core.ports.CompSetRepository
import com.keyway.kommons.db.exception.NoRowFoundException
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import java.time.LocalDate
import java.time.OffsetDateTime

class CompSetRepositoryTest : BaseApplicationTest() {
    private val compSetRepository: CompSetRepository by inject()

    @Test
    fun `could save && update`() {
        val initialCompset = getCompset()
        val updatedCompset = initialCompset.copy(propertyIds = linkedSetOf(anyString(), anyString()))

        compSetRepository.insertOrUpdate(initialCompset)
        compare(compSetRepository.getCompSetById(initialCompset.id), initialCompset)
        compSetRepository.insertOrUpdate(updatedCompset)
        compare(compSetRepository.getCompSetById(initialCompset.id), updatedCompset)
    }

    @Test
    fun `could save and update basePropertyAcquisitionDate`() {
        // Create a CompSet with null basePropertyAcquisitionDate
        val initialCompset = getCompset()

        // Save the CompSet
        compSetRepository.insertOrUpdate(initialCompset)

        // Verify the CompSet was saved correctly
        val savedCompset = compSetRepository.getCompSetById(initialCompset.id)
        compare(savedCompset, initialCompset)
        Assertions.assertNull(savedCompset.basePropertyAcquisitionDate)

        // Update the CompSet with a basePropertyAcquisitionDate
        val acquisitionDate = LocalDate.now()
        val updatedCompset = initialCompset.copy(basePropertyAcquisitionDate = acquisitionDate)
        compSetRepository.insertOrUpdate(updatedCompset)

        // Verify the CompSet was updated correctly
        val retrievedCompset = compSetRepository.getCompSetById(initialCompset.id)
        compare(retrievedCompset, updatedCompset)
        Assertions.assertEquals(acquisitionDate, retrievedCompset.basePropertyAcquisitionDate)

        // Update the CompSet with a different basePropertyAcquisitionDate
        val newAcquisitionDate = LocalDate.now().minusDays(30)
        val newUpdatedCompset = updatedCompset.copy(basePropertyAcquisitionDate = newAcquisitionDate)
        compSetRepository.insertOrUpdate(newUpdatedCompset)

        // Verify the CompSet was updated correctly
        val newRetrievedCompset = compSetRepository.getCompSetById(initialCompset.id)
        compare(newRetrievedCompset, newUpdatedCompset)
        Assertions.assertEquals(newAcquisitionDate, newRetrievedCompset.basePropertyAcquisitionDate)
    }

    @Test
    fun `could get all by organization`() {
        val initialCompsets =
            listOf(
                getCompset(),
                getCompset(),
                getCompset(),
            )
        initialCompsets.forEach(compSetRepository::insertOrUpdate)

        initialCompsets.forEach {
            compare(compSetRepository.getAllCompSets(it.organizationId, null).first(), it)
        }
    }

    @Test
    fun `could get all by organization and user`() {
        val userId = anyString()
        val oneCompSet = getCompset().copy(createdBy = userId)
        val initialCompsets =
            listOf(
                getCompset().copy(createdBy = "ASD"),
                getCompset().copy(createdBy = "ASD"),
                oneCompSet,
            )
        initialCompsets.forEach(compSetRepository::insertOrUpdate)
        compare(compSetRepository.getAllCompSets(oneCompSet.organizationId, userId).first(), oneCompSet)
    }

    @Test
    fun `could delete`() {
        val initialCompset = getCompset()
        compSetRepository.insertOrUpdate(initialCompset)
        compSetRepository.getCompSetById(initialCompset.id)
        compSetRepository.deleteCompSet(initialCompset.id)
        Assertions.assertThrowsExactly(
            NoRowFoundException::class.java,
        ) { compSetRepository.getCompSetById(initialCompset.id) }
    }

    private fun compare(
        compSet1: CompSet,
        compSet2: CompSet,
    ) {
        Assertions.assertEquals(compSet1.id, compSet2.id)
        Assertions.assertEquals(compSet1.propertyIds, compSet2.propertyIds)
        Assertions.assertEquals(compSet1.name, compSet2.name)
        Assertions.assertEquals(compSet1.basePropertyId, compSet2.basePropertyId)
        Assertions.assertEquals(compSet1.basePropertyAcquisitionDate, compSet2.basePropertyAcquisitionDate)
    }

    private fun getCompset() =
        CompSet(
            id = anyString(),
            organizationId = anyString(),
            name = anyString(),
            propertyIds = linkedSetOf(),
            basePropertyId = anyString(),
            createdAt = OffsetDateTime.now(),
            updatedAt = OffsetDateTime.now(),
            createdBy = anyString(),
            basePropertyAcquisitionDate = null,
            basePropertyStage = PropertyStage.OPERATIONAL,
        )
}
