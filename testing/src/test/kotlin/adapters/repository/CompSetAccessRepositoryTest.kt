package adapters.repository

import application.utils.anyString
import application.utils.base.BaseApplicationTest
import com.keyway.core.entities.AccessEntityType
import com.keyway.core.entities.CompSet
import com.keyway.core.entities.CompSetAccessPolicy
import com.keyway.core.entities.Permission
import com.keyway.core.entities.multifamily.PropertyStage
import com.keyway.core.ports.CompSetAccessPolicyRepository
import com.keyway.core.ports.CompSetRepository
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import java.time.OffsetDateTime

class CompSetAccessRepositoryTest : BaseApplicationTest() {
    private val compSetRepository: CompSetRepository by inject()
    private val compSetAccessPolicyRepository: CompSetAccessPolicyRepository by inject()

    private lateinit var organizationId: String
    private lateinit var userId: String
    private lateinit var compSet1: CompSet
    private lateinit var compSet2: CompSet

    @BeforeEach
    fun setUp() {
        organizationId = anyString()
        userId = anyString()

        // Create two comp sets
        compSet1 = getCompset(organizationId)
        compSet2 = getCompset(organizationId)

        compSetRepository.insertOrUpdate(compSet1)
        compSetRepository.insertOrUpdate(compSet2)
    }

    @Test
    fun `should get comp sets with access for organization only`() {
        // Create access policy for organization on compSet1
        val orgPolicy =
            createAccessPolicy(
                compSetId = compSet1.id,
                entityType = AccessEntityType.ORGANIZATION,
                entityId = organizationId,
                permissions = setOf(Permission.VIEW),
            )
        compSetAccessPolicyRepository.save(orgPolicy)

        // Get comp sets with access
        val result = compSetRepository.getCompSetsWithAccess(organizationId, userId)

        // Verify results
        Assertions.assertEquals(1, result.size)
        Assertions.assertEquals(compSet1.id, result[0].id)
    }

    @Test
    fun `should get comp sets with access for user only`() {
        // Create access policy for user on compSet2
        val userPolicy =
            createAccessPolicy(
                compSetId = compSet2.id,
                entityType = AccessEntityType.USER,
                entityId = userId,
                permissions = setOf(Permission.EDIT),
            )
        compSetAccessPolicyRepository.save(userPolicy)

        // Get comp sets with access
        val result = compSetRepository.getCompSetsWithAccess(organizationId, userId)

        // Verify results
        Assertions.assertEquals(1, result.size)
        Assertions.assertEquals(compSet2.id, result[0].id)
    }

    @Test
    fun `should get comp sets with combined permissions when both org and user have access`() {
        // Create access policy for organization on compSet1
        val orgPolicy =
            createAccessPolicy(
                compSetId = compSet1.id,
                entityType = AccessEntityType.ORGANIZATION,
                entityId = organizationId,
                permissions = setOf(Permission.VIEW),
            )
        compSetAccessPolicyRepository.save(orgPolicy)

        // Create access policy for user on same compSet1
        val userPolicy =
            createAccessPolicy(
                compSetId = compSet1.id,
                entityType = AccessEntityType.USER,
                entityId = userId,
                permissions = setOf(Permission.EDIT),
            )
        compSetAccessPolicyRepository.save(userPolicy)

        // Get comp sets with access
        val result = compSetRepository.getCompSetsWithAccess(organizationId, userId)

        // Verify results
        Assertions.assertEquals(1, result.size)
        Assertions.assertEquals(compSet1.id, result[0].id)
    }

    @Test
    fun `should get multiple comp sets with access`() {
        // Create access policy for organization on compSet1
        val orgPolicy1 =
            createAccessPolicy(
                compSetId = compSet1.id,
                entityType = AccessEntityType.ORGANIZATION,
                entityId = organizationId,
                permissions = setOf(Permission.VIEW),
            )
        compSetAccessPolicyRepository.save(orgPolicy1)

        // Create access policy for user on compSet2
        val userPolicy =
            createAccessPolicy(
                compSetId = compSet2.id,
                entityType = AccessEntityType.USER,
                entityId = userId,
                permissions = setOf(Permission.EDIT),
            )
        compSetAccessPolicyRepository.save(userPolicy)

        // Get comp sets with access
        val result = compSetRepository.getCompSetsWithAccess(organizationId, userId)

        // Verify results
        Assertions.assertEquals(2, result.size)

        // Sort results by ID to make assertions easier
        val sortedResults = result.sortedBy { it.id }

        // First comp set should have VIEW permission
        val firstResult = sortedResults.find { it.id == compSet1.id }
        Assertions.assertNotNull(firstResult)

        // Second comp set should have EDIT permission
        val secondResult = sortedResults.find { it.id == compSet2.id }
        Assertions.assertNotNull(secondResult)
    }

    private fun getCompset(orgId: String = anyString()) =
        CompSet(
            id = anyString(),
            organizationId = orgId,
            name = anyString(),
            propertyIds = linkedSetOf(),
            basePropertyId = anyString(),
            createdAt = OffsetDateTime.now(),
            updatedAt = OffsetDateTime.now(),
            createdBy = anyString(),
            basePropertyStage = PropertyStage.OPERATIONAL,
        )

    private fun createAccessPolicy(
        compSetId: String,
        entityType: AccessEntityType,
        entityId: String,
        permissions: Set<Permission>,
    ): CompSetAccessPolicy =
        CompSetAccessPolicy(
            id = anyString(),
            compSetId = compSetId,
            entityType = entityType,
            entityId = entityId,
            permissions = permissions,
            createdAt = OffsetDateTime.now(),
            createdBy = anyString(),
        )
}
