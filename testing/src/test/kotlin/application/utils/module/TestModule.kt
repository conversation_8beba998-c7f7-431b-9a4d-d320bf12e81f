package application.utils.module

import adapters.gateway.OrganizationsApiTestGateway
import application.security.AccessTokenGenerator
import application.utils.router.TestRouter
import com.auth0.jwt.algorithms.Algorithm
import com.keyway.application.koin.ModuleConstants.ROUTES
import com.keyway.application.router.comparables.ComparableRouter
import com.keyway.application.router.compset.CompSetAccessPoliciesRouter
import com.keyway.application.router.compset.CompSetRouter
import com.keyway.application.router.health.HealthCheckRouter
import com.keyway.application.router.property.PropertyRouter
import com.keyway.application.utils.koin.createInstanceBy
import com.keyway.core.gateways.UserGateway
import com.keyway.security.domain.algorithm.AlgorithmRepository
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import org.koin.core.qualifier.named
import org.koin.dsl.module

object TestModule {
    val modules =
        module(createdAtStart = true) {

            single<AlgorithmRepository> {
                mockk<AlgorithmRepository>().also {
                    every {
                        it.findByKeyId(any())
                    } returns Algorithm.HMAC256("secret")
                }
            }

            single { AccessTokenGenerator(get(), get(), get()) }

            single<UserGateway> { spyk(OrganizationsApiTestGateway()) }

            // Routers
            single(named(ROUTES)) {
                setOf(
                    createInstanceBy(::TestRouter),
                    createInstanceBy(::HealthCheckRouter),
                    createInstanceBy(::CompSetRouter),
                    createInstanceBy(::ComparableRouter),
                    createInstanceBy(::CompSetAccessPoliciesRouter),
                    createInstanceBy(::PropertyRouter),
                )
            }
        }
}
