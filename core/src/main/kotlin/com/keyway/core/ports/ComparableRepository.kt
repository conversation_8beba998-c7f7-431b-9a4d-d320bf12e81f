package com.keyway.core.ports

import com.keyway.core.dto.PropertyCompsResult
import com.keyway.core.entities.PropertySimilarityInput
import com.keyway.core.entities.SimilarityWeight
import com.keyway.core.entities.search.PropertyCompsPaginatedInput

interface ComparableRepository {
    suspend fun getPropertyComps(
        property: PropertySimilarityInput,
        similarityWeights: List<SimilarityWeight>,
        paginatedInput: PropertyCompsPaginatedInput,
        propertyIds: Set<String>,
    ): PropertyCompsResult
}
