package com.keyway.core.usecases.properties.comparable

import com.keyway.core.dto.PropertyCompsResult
import com.keyway.core.entities.PropertySimilarityInput
import com.keyway.core.entities.multifamily.BaseMultifamilyProperty
import com.keyway.core.entities.multifamily.OperationalMultifamilyProperty
import com.keyway.core.entities.search.PropertyCompsPaginatedInput
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.ports.ComparableRepository
import com.keyway.core.ports.MultifamilyPropertyRepository
import com.keyway.core.ports.PropertyRepository
import com.keyway.core.ports.SimilarityConfigurationRepository
import com.keyway.core.usecases.UseCaseAsync
import org.slf4j.LoggerFactory
import java.util.UUID

class GetPropertyComparable(
    private val multifamilyPropertyRepository: MultifamilyPropertyRepository,
    private val propertyRepository: PropertyRepository,
    private val comparableRepository: ComparableRepository,
    private val similarityConfigurationRepository: SimilarityConfigurationRepository,
) : UseCaseAsync<GetPropertyComparable.Input, PropertyCompsResult> {
    private val logger = LoggerFactory.getLogger(this.javaClass)

    data class Input(
        val propertyId: String,
        val organizationId: String,
        val paginatedInput: PropertyCompsPaginatedInput,
        val propertyIds: Set<String> = emptySet(),
    )

    override suspend fun execute(input: Input): PropertyCompsResult =
        kotlin
            .runCatching {
                input.getPropertySimilarityInput()
            }.onFailure {
                logger.warn("Unable to find property with id ${input.propertyId}", it)
                throw NotFoundException("Unable to find property with id ${input.propertyId}")
            }.getOrThrow()
            .let {
                comparableRepository.getPropertyComps(
                    it,
                    similarityConfigurationRepository.getSimilarityConfiguration(
                        input.organizationId,
                        input.paginatedInput.scoreType,
                    ),
                    input.paginatedInput,
                    propertyIds = input.propertyIds,
                )
            }

    private fun Input.getPropertySimilarityInput(): PropertySimilarityInput =
        when {
            isValidUUID(this.propertyId) -> propertyRepository.getById(this.propertyId).toPropertySimilarityInput()
            else -> multifamilyPropertyRepository.getByPropertyId(this.propertyId).toPropertySimilarityInput()
        }

    private fun isValidUUID(uuidString: String): Boolean =
        try {
            UUID.fromString(uuidString)
            true
        } catch (e: IllegalArgumentException) {
            false
        }

    private fun OperationalMultifamilyProperty.toPropertySimilarityInput(): PropertySimilarityInput =
        PropertySimilarityInput(
            id = this.id,
            geolocation = this.geolocation,
            units = this.units,
            unitQuantity = this.unitQuantity,
            constructionYear = this.constructionYear,
            propertyAmenities = this.propertyAmenities,
            unitsAmenities = this.unitsAmenities,
            qualityOverallScore = this.qualityOverallScore,
            propertyStyle = this.propertyStyle,
            stories = this.stories,
            squareFootagePerUnit = this.squareFootagePerUnit,
        )

    private fun BaseMultifamilyProperty.toPropertySimilarityInput(): PropertySimilarityInput =
        PropertySimilarityInput(
            id = this.id,
            geolocation = this.geolocation,
            units = emptyList(),
            unitQuantity = this.unitsQuantity,
            constructionYear = this.constructionYear,
            propertyAmenities = this.propertyAmenities?.map { it.name }?.toSet() ?: emptySet(),
            unitsAmenities = this.unitsAmenities?.map { it.name }?.toSet() ?: emptySet(),
            qualityOverallScore = null,
            propertyStyle = this.propertyStyle?.name,
            stories = this.stories,
            squareFootagePerUnit = null,
        )
}
