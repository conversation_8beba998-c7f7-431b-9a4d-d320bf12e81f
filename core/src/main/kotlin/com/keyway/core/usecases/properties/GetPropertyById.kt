package com.keyway.core.usecases.properties

import com.keyway.core.entities.multifamily.BaseMultifamilyProperty
import com.keyway.core.ports.PropertyRepository
import com.keyway.core.usecases.UseCaseAsync
import org.slf4j.LoggerFactory

class GetPropertyById(
    private val propertyRepository: PropertyRepository,
) : UseCaseAsync<GetPropertyById.Input, BaseMultifamilyProperty> {
    private val logger = LoggerFactory.getLogger(this.javaClass)

    data class Input(
        val propertyId: String,
        val organizationId: String,
    )

    override suspend fun execute(input: Input): BaseMultifamilyProperty =
        propertyRepository.getById(input.propertyId).also {
            if (it.organizationId != input.organizationId) {
                // TODO IMPLEMENT PERMISSIONS
                logger.warn("Property ${input.propertyId} not match with ${input.organizationId}")
            }
        }
}
