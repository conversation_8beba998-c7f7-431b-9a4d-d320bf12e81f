package com.keyway.core.usecases.properties

import com.keyway.core.entities.multifamily.BaseMultifamilyProperty
import com.keyway.core.ports.PropertyRepository
import com.keyway.core.usecases.UseCaseAsync

class PartialUpdateProperty(
    private val repository: PropertyRepository,
) : UseCaseAsync<PartialUpdateProperty.Input, BaseMultifamilyProperty> {


    override suspend fun execute(input: Input): BaseMultifamilyProperty {
        TODO("Not yet implemented")
    }

    data class Input(
       val propertyId: String,
       val newData: Map<String, Any?>,
    )



}
