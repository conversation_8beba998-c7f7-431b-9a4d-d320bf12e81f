package com.keyway.core.usecases.properties

import com.keyway.core.entities.GeoPoint
import com.keyway.core.entities.multifamily.BaseMultifamilyProperty
import com.keyway.core.entities.multifamily.MultifamilyHousingSegment
import com.keyway.core.entities.multifamily.MultifamilyPropertyAmenity
import com.keyway.core.entities.multifamily.MultifamilyPropertyStyle
import com.keyway.core.entities.multifamily.MultifamilyUnitAmenity
import com.keyway.core.entities.multifamily.PropertyStage
import com.keyway.core.gateways.UserGateway
import com.keyway.core.ports.PropertyRepository
import com.keyway.core.usecases.IdGenerator
import com.keyway.core.usecases.UseCaseAsync
import java.time.Clock
import java.time.OffsetDateTime

class CreateProperty(
    private val repository: PropertyRepository,
    private val idGenerator: IdGenerator,
    private val clock: Clock,
    private val userGateway: UserGateway,
) : UseCaseAsync<CreateProperty.Input, BaseMultifamilyProperty> {
    data class Input(
        val name: String,
        val address: String,
        val city: String,
        val state: String,
        val zipCode: String,
        val location: GeoPoint,
        val propertyAmenities: List<MultifamilyPropertyAmenity>? = listOf(),
        val unitsAmenities: List<MultifamilyUnitAmenity>? = listOf(),
        val housingSegments: List<MultifamilyHousingSegment>? = listOf(),
        val propertyStyle: MultifamilyPropertyStyle? = null,
        val unitsQuantity: Int,
        val constructionYear: Int? = null,
        val organizationId: String,
        val token: String,
    )

    override suspend fun execute(input: Input): BaseMultifamilyProperty {
        val propertyId = idGenerator.invoke()
        val now = OffsetDateTime.now(clock)

        val property =
            BaseMultifamilyProperty(
                id = propertyId,
                name = input.name,
                address = input.address,
                city = input.city,
                state = input.state,
                zipCode = input.zipCode,
                location = input.location,
                geolocation = "POINT(${input.location.longitude} ${input.location.latitude})",
                propertyAmenities = input.propertyAmenities ?: emptyList(),
                unitsAmenities = input.unitsAmenities ?: emptyList(),
                housingSegments = input.housingSegments ?: emptyList(),
                propertyStyle = input.propertyStyle,
                unitsQuantity = input.unitsQuantity,
                constructionYear = input.constructionYear,
                stage = PropertyStage.DEVELOPING,
                organizationId = input.organizationId,
                createdBy = userGateway.getUserByToken(input.token)?.id,
                createdAt = now,
                updatedAt = now,
            )

        repository.saveOrUpdate(property)
        return property
    }
}
