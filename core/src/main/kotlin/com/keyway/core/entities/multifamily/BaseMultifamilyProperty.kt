package com.keyway.core.entities.multifamily

import com.keyway.core.entities.GeoPoint
import java.time.OffsetDateTime

data class BaseMultifamilyProperty(
    val id: String,
    val name: String,
    val address: String,
    val city: String,
    val state: String,
    val zipCode: String,
    val location: GeoPoint,
    val geolocation: String,
    val propertyAmenities: List<MultifamilyPropertyAmenity>? = listOf(),
    val unitsAmenities: List<MultifamilyUnitAmenity>? = listOf(),
    val housingSegments: List<MultifamilyHousingSegment>? = listOf(),
    val propertyStyle: MultifamilyPropertyStyle? = null,
    val unitsQuantity: Int,
    val constructionYear: Int? = null,
    val stage: PropertyStage,
    val stories: Int? = null,
    val organizationId: String,
    val createdBy: String?,
    val createdAt: OffsetDateTime,
    val updatedAt: OffsetDateTime,
)
