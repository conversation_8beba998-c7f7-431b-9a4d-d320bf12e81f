package com.keyway.core.entities

import java.math.BigDecimal

data class PropertySimilarityInput(
    val id: String,
    val geolocation: String,
    val units: List<UnitMix>,
    val unitQuantity: Int,
    val constructionYear: Int?,
    val propertyAmenities: Set<String> = emptySet(),
    val unitsAmenities: Set<String> = emptySet(),
    val qualityOverallScore: BigDecimal? = null,
    val propertyStyle: String? = null,
    val stories: Int? = null,
    val squareFootagePerUnit: BigDecimal?,
)
